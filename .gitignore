# Binance-Lighter 套利交易系统 .gitignore

# ============================================================================
# Python 相关文件
# ============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# C 扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django 
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx 文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# ============================================================================
# 项目特定文件
# ============================================================================

# 数据库文件
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite
data/*.sqlite3

# 数据备份
data/backups/
backups/

# 进程锁文件
*.lock
data/*.lock

# 日志文件
logs/
*.log
*.log.*

# 临时数据文件
data/temp/
data/tmp/
temp/
tmp/

# ============================================================================
# 敏感配置文件
# ============================================================================

# ⚠️ 重要：包含API密钥的配置文件
config/exchanges.yaml

# 其他敏感配置
config/secrets.yaml
config/*.secret
config/*.key
config/*.pem

# 环境特定配置
config/local.yaml
config/dev.yaml

# 密钥文件
*.key
*.pem
*.p12
*.pfx

# ============================================================================
# IDE 和编辑器文件
# ============================================================================

# VS Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# 系统文件
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# 开发和测试文件
# ============================================================================

# 测试数据
test_data/
test_logs/
test_output/

# 性能分析
*.prof
*.cprof

# 调试文件
debug.log
debug_*.log

# ============================================================================
# 部署和运维文件
# ============================================================================

# Docker
.dockerignore
docker-compose.override.yml

# 监控和日志
monitoring/
alerts/

# 部署脚本
deploy_secrets.sh
production_deploy.sh

# ============================================================================
# 其他
# ============================================================================

# 文档构建
docs/build/
docs/_build/

# 缓存文件
.cache/
*.cache

# 用户自定义配置
user_config.yaml
personal_settings.yaml

# 备注文件
TODO.txt
NOTES.txt
*.notes

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# ============================================================================
# 交易系统特定忽略项
# ============================================================================

# 历史数据文件
data/historical/
data/market_data/
data/price_history/

# 策略测试结果
backtest_results/
strategy_reports/

# 实时数据缓存
data/cache/
data/realtime/

# 错误转储
core
*.dump

# 系统监控数据
system_metrics/
performance_logs/

# ============================================================================
# 注意事项
# ============================================================================
# 
# 1. API密钥和敏感信息永远不要提交到版本控制
# 2. 数据库文件通常不应提交，除非是初始化数据
# 3. 日志文件应该在运行时生成，不要提交到仓库
# 4. 个人配置文件应该被忽略，只提交模板文件
# 5. 定期检查是否有新的文件类型需要忽略 