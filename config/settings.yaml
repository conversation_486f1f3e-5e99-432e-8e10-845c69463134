advanced:
  cluster_nodes: []
  distributed: false
  ml_enabled: false
  model_path: models/
  multi_strategy: false
  strategy_weights: {}
arbitrage:
  thresholds:
    max_spread_pct: 0.02
    min_spread_pct: 0.002
    target_spread_pct: 0.005
backup:
  backup_directory: backups/
  backup_interval: 3600
  enabled: true
  include_config: true
  include_data: true
  include_logs: true
  max_backups: 24
database:
  auto_backup: true
  auto_commit_interval: 60
  backup_dir: data/backups
  backup_interval_hours: 24
  cache_size: 1000
  data_retention_days: 90
  path: data/arbitrage.db
development:
  debug: false
  mock_exchanges: false
  paper_trading: true
  profile_output: profiles/
  profiling: false
  test_mode: false
exchange_settings:
  binance:
    order_type: limit
    recv_window: 5000
    time_in_force: GTC
  lighter:
    max_slippage: 0.001
    order_type: market
exchanges:
  binance:
    api_key: demo_api_key_for_paper_trading
    sandbox: true
    secret: demo_secret_key_for_paper_trading
    testnet: true
  config_file: exchanges.yaml
  lighter:
    account_index: 595
    api_key_index: 1
    api_url: https://api.lighter.xyz
    private_key: demo_private_key_for_paper_trading
logging:
  backup_count: 5
  colored_logs: true
  console_logging: true
  level: INFO
  log_file: logs/arbitrage.log
  log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  max_file_size: 10MB
  modules:
    database: INFO
    risk: WARNING
    trading: INFO
    websocket: ERROR
  structured_logging: true
  trade_log_file: logs/trades.log
  trade_log_format: json
maintenance:
  auto_restart: false
  backup_on_shutdown: true
  cleanup_interval: 86400
  health_check_interval: 300
  restart_time: 02:00
monitoring:
  alerts:
    email_notifications: false
    enabled: true
    slack_webhook: null
    webhook_url: null
  database:
    enabled: false
    type: sqlite
    url: sqlite:///data/arbitrage.db
  metrics:
    collection_interval: 5
    enabled: true
    retention_period: 86400
  web_debug: false
  web_host: localhost
  web_port: 8000
network:
  connection_timeout: 10
  keepalive: true
  proxy:
    enabled: false
    http: null
    https: null
  read_timeout: 30
  retry_attempts: 3
  retry_delay: 1
notifications:
  enabled: false
  methods:
    email:
      enabled: false
      from_address: ''
      password: ''
      smtp_port: 587
      smtp_server: ''
      to_addresses: []
      username: ''
    telegram:
      bot_token: ''
      chat_id: ''
      enabled: false
    webhook:
      enabled: false
      headers: {}
      url: ''
performance:
  data_buffer_size: 1000
  gc_threshold: 0.8
  max_concurrent_requests: 10
  max_memory_usage: 512MB
  processing_interval: 0.1
  request_timeout: 30
risk_management:
  alert_levels:
    critical: 0.9
    high: 0.8
    low: 0.3
    medium: 0.6
  latency_threshold: 1000
  max_exposure: 1000
  max_loss_per_day: 50
  position_limit_pct: 0.3
  price_deviation_threshold: 0.1
  stop_loss_pct: 0.02
security:
  api_key_encryption: false
  max_requests_per_minute: 60
  rate_limiting: true
  session_timeout: 3600
  whitelist_ips: []
strategy:
  parameters:
    cooldown_period: 60
    ma_fast: 5
    ma_slow: 20
    signal_threshold: 0.001
  signal_filters:
    max_spread: 0.02
    min_spread: 0.002
    min_volume: 0.01
    time_windows:
      enabled: false
      end_hour: 24
      start_hour: 0
  type: ma_based
trading:
  base_currency: BTC
  enabled: true
  ma_period: 20
  max_order_retries: 3
  max_position_size: 0.05
  max_spread_threshold: 0.01
  max_trade_amount: 0.005
  min_profit_threshold: 0.001
  min_trade_amount: 0.001
  order_refresh_interval: 5
  order_timeout: 60
  quote_currency: USDT
  symbol: BTC/USDT
