"""
套利交易引擎

协调Binance和Lighter客户端，执行套利交易策略
"""

import asyncio
import time
import psutil
from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, Optional, Any, List
import structlog

from ..exchanges.binance_client import BinanceClient, OrderBook as BinanceOrderBook, Trade as BinanceTrade
from ..exchanges.lighter_client import LighterClient, LighterOrderBook, LighterTrade
from .strategy import ArbitrageStrategy, SignalType, ArbitrageSignal, OrderInfo
from .risk_manager import RiskManager
from ..database.database import DatabaseManager
from ..database.trade_recorder import TradeRecorder
from ..database.models import (
    TradeType, OrderSide, SystemStatus, RiskMetrics, TradeStatus
)

# 导入新的监控和优化模块
try:
    from ..monitoring.system_monitor import SystemMonitor
    MONITORING_AVAILABLE = True
except ImportError:
    SystemMonitor = None
    MONITORING_AVAILABLE = False

try:
    from ..trading.order_manager import OrderManager
    ORDER_MANAGER_AVAILABLE = True
except ImportError:
    OrderManager = None
    ORDER_MANAGER_AVAILABLE = False

try:
    from ..optimization.performance_optimizer import PerformanceOptimizer
    OPTIMIZATION_AVAILABLE = True
except ImportError:
    PerformanceOptimizer = None
    OPTIMIZATION_AVAILABLE = False
from ..utils.logger import trading_logger
from ..utils.config_loader import config

logger = structlog.get_logger(__name__)


class ArbitrageEngine:
    """套利交易引擎"""

    def __init__(self, config_dict: Dict[str, Any]):
        """
        初始化套利引擎

        Args:
            config_dict: 配置字典
        """
        self.config = config_dict

        # 初始化交易所客户端
        self.binance_client: Optional[BinanceClient] = None
        self.lighter_client: Optional[LighterClient] = None

        # 初始化策略和风险管理
        self.strategy = ArbitrageStrategy(config_dict.get('trading', {}))
        self.risk_manager = RiskManager(config_dict.get('risk_management', {}))

        # 初始化数据库和记录器
        self.db_manager = DatabaseManager(
            db_path=config_dict.get('database', {}).get('path', 'data/arbitrage.db'),
            backup_dir=config_dict.get('database', {}).get('backup_dir', 'data/backups')
        )
        self.trade_recorder: Optional[TradeRecorder] = None

        # Web界面回调函数（用于实时数据推送）
        self.web_price_update_callback: Optional[callable] = None
        self.web_status_update_callback: Optional[callable] = None
        self.web_log_callback: Optional[callable] = None
        self.web_trade_update_callback: Optional[callable] = None

        # 运行状态
        self.is_running = False
        self.is_initialized = False
        self.is_trading_enabled = True
        # 优先从development配置读取，然后从trading配置读取
        self.is_paper_trading = (
            config_dict.get('development', {}).get('paper_trading', False) or
            config_dict.get('trading', {}).get('paper_trading', False)
        )

        # 连接状态
        self.connection_status = {
            'binance': False,
            'lighter': False,
            'websocket': False
        }

        # 统计信息
        self.stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_profit': 0.0,
            'start_time': time.time(),
            'last_trade_time': None,
            'error_count': 0
        }

        # 标记统计数据是否已从数据库加载
        self._stats_loaded = False

        # 性能监控
        self.last_price_update = datetime.now(timezone.utc)

        # 新增：集成监控和优化模块
        self.system_monitor = None
        self.order_manager = None
        self.performance_optimizer = None

        # 初始化扩展功能
        self._initialize_extensions()

        logger.info("套利引擎初始化完成",
                   monitoring_available=MONITORING_AVAILABLE,
                   order_manager_available=ORDER_MANAGER_AVAILABLE,
                   optimization_available=OPTIMIZATION_AVAILABLE)

    def _initialize_extensions(self):
        """初始化扩展功能"""
        try:
            # 初始化系统监控
            if MONITORING_AVAILABLE:
                self.system_monitor = SystemMonitor(self.config)
                logger.info("✅ 系统监控模块已初始化")
            else:
                logger.warning("⚠️ 系统监控模块不可用")

            # 初始化订单管理器
            if ORDER_MANAGER_AVAILABLE:
                self.order_manager = OrderManager(self.config)
                logger.info("✅ 订单管理器已初始化")
            else:
                logger.warning("⚠️ 订单管理器不可用")

            # 初始化性能优化器
            if OPTIMIZATION_AVAILABLE:
                self.performance_optimizer = PerformanceOptimizer(self.config)
                logger.info("✅ 性能优化器已初始化")
            else:
                logger.warning("⚠️ 性能优化器不可用")

        except Exception as e:
            logger.error("扩展功能初始化失败", error=str(e))

    async def initialize(self) -> None:
        """初始化引擎"""
        try:
            logger.info("开始初始化套利引擎")

            # 初始化数据库
            await self.db_manager.initialize()
            self.trade_recorder = TradeRecorder(self.db_manager)

            # 初始化Binance客户端
            binance_config = self.config['exchanges']['binance']
            self.binance_client = BinanceClient(
                api_key=binance_config['api_key'],
                secret=binance_config['secret'],
                sandbox=binance_config.get('sandbox', True),
                testnet=binance_config.get('testnet', True),
                is_paper_trading=self.is_paper_trading
            )
            await self.binance_client.initialize()
            self.connection_status['binance'] = True

            # 初始化Lighter客户端
            lighter_config = self.config['exchanges']['lighter']
            self.lighter_client = LighterClient(
                symbol=self.config['trading']['symbol'],
                is_paper_trading=self.is_paper_trading
            )
            lighter_initialized = await self.lighter_client.initialize()
            if not lighter_initialized:
                raise Exception("Lighter客户端初始化失败")
            self.connection_status['lighter'] = True

            # 设置回调函数
            self._setup_callbacks()

            # 订阅市场数据
            symbol = self.config['trading']['symbol']
            await self._subscribe_market_data(symbol)
            self.connection_status['websocket'] = True

            # 初始化扩展模块
            await self._initialize_extension_modules()

            self.is_initialized = True
            logger.info("套利引擎初始化成功")

            # 加载历史统计数据
            await self._load_stats_from_database()

            # 记录系统状态
            await self._record_system_status()

        except Exception as e:
            logger.error("套利引擎初始化失败", error=str(e))
            self.stats['error_count'] += 1
            raise

    async def _initialize_extension_modules(self):
        """初始化扩展模块"""
        try:
            # 初始化系统监控
            if self.system_monitor:
                self.system_monitor.set_arbitrage_engine(self)
                logger.info("✅ 系统监控已连接到套利引擎")

            # 初始化订单管理器
            if self.order_manager:
                self.order_manager.set_exchange_clients(self.binance_client, self.lighter_client)
                logger.info("✅ 订单管理器已连接到交易所客户端")

            # 性能优化器无需额外初始化
            if self.performance_optimizer:
                logger.info("✅ 性能优化器已准备就绪")

        except Exception as e:
            logger.error("扩展模块初始化失败", error=str(e))

    async def _load_stats_from_database(self):
        """从数据库加载统计数据"""
        try:
            if not self.trade_recorder or self._stats_loaded:
                return

            # 获取实际的交易统计
            stats = await self.trade_recorder.get_trading_statistics()

            if stats:
                # 更新统计数据
                self.stats.update({
                    'total_trades': stats.get('total_trades', 0),
                    'successful_trades': stats.get('successful_trades', 0),
                    'failed_trades': stats.get('failed_trades', 0),
                    'total_profit': float(stats.get('total_profit', 0.0)),
                    'last_trade_time': stats.get('last_trade_time')
                })

                self._stats_loaded = True
                logger.info("统计数据已从数据库加载",
                           total_trades=self.stats['total_trades'],
                           successful_trades=self.stats['successful_trades'],
                           total_profit=self.stats['total_profit'])
            else:
                logger.info("数据库中暂无交易统计数据")

        except Exception as e:
            logger.error("从数据库加载统计数据失败", error=str(e))

    async def _update_stats_display(self):
        """更新统计数据显示"""
        try:
            # 如果还没有从数据库加载统计数据，先加载
            if not self._stats_loaded:
                await self._load_stats_from_database()

            # 推送统计数据到Web界面
            if self.web_status_update_callback:
                status = self.get_status()
                await self.web_status_update_callback(status)

            logger.debug("统计数据显示已更新",
                        total_trades=self.stats['total_trades'],
                        total_profit=self.stats['total_profit'])

        except Exception as e:
            logger.error("更新统计数据显示失败", error=str(e))

    def _setup_callbacks(self) -> None:
        """设置回调函数"""
        # Binance回调
        self.binance_client.set_orderbook_callback(self._on_binance_orderbook)
        self.binance_client.set_trade_callback(self._on_binance_trade)

        # Lighter回调 - 设置订单簿更新回调
        if hasattr(self.lighter_client, 'set_orderbook_callback'):
            self.lighter_client.set_orderbook_callback(self._on_lighter_orderbook)

    async def _subscribe_market_data(self, symbol: str) -> None:
        """订阅市场数据"""
        try:
            # 订阅Binance数据
            await self.binance_client.subscribe_orderbook(symbol)
            await self.binance_client.subscribe_trade(symbol)

            # Lighter客户端在初始化时已经自动订阅数据

            logger.info("市场数据订阅成功", symbol=symbol)

        except Exception as e:
            logger.error("市场数据订阅失败", symbol=symbol, error=str(e))
            self.stats['error_count'] += 1
            raise

    def _on_binance_orderbook(self, orderbook: BinanceOrderBook) -> None:
        """处理Binance订单簿更新"""
        try:
            if orderbook.bids and orderbook.asks:
                bid_price = Decimal(str(orderbook.bids[0][0]))
                ask_price = Decimal(str(orderbook.asks[0][0]))
                bid_amount = Decimal(str(orderbook.bids[0][1]))
                ask_amount = Decimal(str(orderbook.asks[0][1]))

                # 更新策略价格
                self.strategy.update_binance_prices(float(bid_price), float(ask_price))

                # 立即通知Web界面价格更新
                if self.web_price_update_callback:
                    try:
                        strategy_status = self.strategy.get_strategy_status()
                        self.web_price_update_callback({
                            "binance_prices": strategy_status.get('binance_prices', {}),
                            "lighter_prices": strategy_status.get('lighter_prices', {}),
                            "spread_ma_value": strategy_status.get('spread_ma_value'),
                            "current_signal": strategy_status.get('current_signal')
                        })
                    except Exception as e:
                        logger.debug("Web价格回调失败", error=str(e))

                # 记录价格数据 - 使用安全的异步任务创建
                if self.trade_recorder:
                    try:
                        # 安全创建异步任务
                        task = self._safe_create_task(
                            self._safe_record_data(
                                self.trade_recorder.record_price_data,
                                symbol=self.config['trading']['symbol'],
                                exchange="binance",
                                bid_price=bid_price,
                                ask_price=ask_price,
                                last_price=self.strategy.binance_prices.get('last', ask_price),
                                bid_quantity=bid_amount,
                                ask_quantity=ask_amount
                            )
                        )
                    except Exception as e:
                        logger.debug("记录Binance价格数据失败", error=str(e))

                # 更新价格更新时间
                self.last_price_update = datetime.now(timezone.utc)

                # 触发交易逻辑 - 直接调用同步版本
                try:
                    self._process_trading_logic_sync()
                except Exception as e:
                    logger.debug("处理Binance交易逻辑失败", error=str(e))

        except Exception as e:
            logger.error("处理Binance订单簿失败", error=str(e))
            self.stats['error_count'] += 1

    def _on_lighter_orderbook(self, orderbook: LighterOrderBook) -> None:
        """处理Lighter订单簿更新"""
        try:
            if orderbook.bids and orderbook.asks:
                bid_price = Decimal(str(orderbook.bids[0][0]))
                ask_price = Decimal(str(orderbook.asks[0][0]))
                bid_amount = Decimal(str(orderbook.bids[0][1]))
                ask_amount = Decimal(str(orderbook.asks[0][1]))

                # 更新策略价格
                self.strategy.update_lighter_prices(float(bid_price), float(ask_price))

                # 立即通知Web界面价格更新
                if self.web_price_update_callback:
                    try:
                        strategy_status = self.strategy.get_strategy_status()
                        self.web_price_update_callback({
                            "binance_prices": strategy_status.get('binance_prices', {}),
                            "lighter_prices": strategy_status.get('lighter_prices', {}),
                            "spread_ma_value": strategy_status.get('spread_ma_value'),
                            "current_signal": strategy_status.get('current_signal')
                        })
                    except Exception as e:
                        logger.debug("Web价格回调失败", error=str(e))

                # 记录价格数据 - 使用安全的异步任务创建
                if self.trade_recorder:
                    try:
                        # 安全创建异步任务
                        task = self._safe_create_task(
                            self._safe_record_data(
                                self.trade_recorder.record_price_data,
                                symbol=self.config['trading']['symbol'],
                                exchange="lighter",
                                bid_price=bid_price,
                                ask_price=ask_price,
                                last_price=self.strategy.lighter_prices.get('last', ask_price),
                                bid_quantity=bid_amount,
                                ask_quantity=ask_amount
                            )
                        )
                    except Exception as e:
                        logger.debug("记录Lighter价格数据失败", error=str(e))

                # 更新价格更新时间
                self.last_price_update = datetime.now(timezone.utc)

                # 触发交易逻辑 - 直接调用同步版本
                try:
                    self._process_trading_logic_sync()
                except Exception as e:
                    logger.debug("处理Lighter交易逻辑失败", error=str(e))

        except Exception as e:
            logger.error("处理Lighter订单簿失败", error=str(e))
            self.stats['error_count'] += 1

    def _on_binance_trade(self, trade: BinanceTrade) -> None:
        """处理Binance交易更新"""
        try:
            # 更新最新成交价
            self.strategy.binance_prices['last'] = trade.price

        except Exception as e:
            logger.error("处理Binance交易失败", error=str(e))
            self.stats['error_count'] += 1

    def _on_lighter_trade(self, trade: LighterTrade) -> None:
        """处理Lighter交易更新"""
        try:
            # 更新最新成交价
            self.strategy.lighter_prices['last'] = trade.price

        except Exception as e:
            logger.error("处理Lighter交易失败", error=str(e))
            self.stats['error_count'] += 1

    def _process_trading_logic_sync(self) -> None:
        """处理交易逻辑（同步版本）"""
        try:
            if not self.is_running or not self.is_trading_enabled:
                logger.debug("交易逻辑跳过", is_running=self.is_running, is_trading_enabled=self.is_trading_enabled)
                return

            # 检查价格数据完整性
            binance_prices = self.strategy.binance_prices
            lighter_prices = self.strategy.lighter_prices

            logger.debug("当前价格状态",
                        binance_prices=binance_prices,
                        lighter_prices=lighter_prices)

            # 确保两个交易所都有价格数据
            if (binance_prices['bid'] <= 0 or binance_prices['ask'] <= 0 or
                lighter_prices['bid'] <= 0 or lighter_prices['ask'] <= 0):
                logger.debug("价格数据不完整，跳过交易逻辑")
                return

            # 获取当前Lighter价格
            try:
                lighter_orderbook = self.lighter_client.get_orderbook(self.config['trading']['symbol'])

                # 更新策略价格
                if lighter_orderbook['bids'] and lighter_orderbook['asks']:
                    bid_price = float(lighter_orderbook['bids'][0][0])
                    ask_price = float(lighter_orderbook['asks'][0][0])

                    # 尝试获取真实成交价
                    try:
                        lighter_price = self.lighter_client.get_current_price(self.config['trading']['symbol'])
                        # 更新策略价格（包含最新价）
                        self.strategy.update_lighter_prices(bid_price, ask_price, lighter_price)
                    except Exception:
                        # 如果获取不到真实成交价，只传递买卖价，让策略计算中间价
                        self.strategy.update_lighter_prices(bid_price, ask_price)

            except Exception as e:
                logger.debug("获取Lighter价格失败", error=str(e))
                return

            # 分析价差并生成信号
            signal = self.strategy.analyze_spread()
            if not signal:
                logger.debug("未生成交易信号")
                return

            logger.info("生成交易信号", signal_type=signal.signal_type, diff_rate=signal.diff_rate, confidence=signal.confidence)

            # 记录价差数据（异步版本）
            if self.trade_recorder:
                try:
                    # 创建异步任务
                    asyncio.create_task(self._record_spread_data(signal))
                except Exception as e:
                    logger.debug("记录价差数据失败", error=str(e))

            # 检查风险管理（异步版本）
            try:
                # 创建异步任务来检查风险管理
                asyncio.create_task(self._check_risk_management_and_execute(signal))
                return  # 让异步任务处理后续逻辑
            except Exception as e:
                logger.debug("风险管理检查失败", error=str(e))
                return

        except Exception as e:
            logger.error("处理交易逻辑失败", error=str(e))
            self.stats['error_count'] += 1

    def _safe_create_task(self, coro):
        """安全创建异步任务"""
        try:
            loop = asyncio.get_running_loop()
            return loop.create_task(coro)
        except RuntimeError:
            # 如果没有运行的事件循环，直接返回None
            logger.debug("没有运行的事件循环，跳过任务创建")
            return None

    async def _safe_record_data(self, record_func, *args, **kwargs):
        """安全记录数据"""
        try:
            await record_func(*args, **kwargs)
        except Exception as e:
            logger.debug(f"记录数据失败: {e}")

    async def _check_risk_management_and_execute(self, signal) -> None:
        """检查风险管理并执行交易"""
        try:
            # 检查风险管理
            if not await self._check_risk_management():
                logger.debug("风险管理检查未通过")
                return

            # 检查是否应该下单
            should_place, reason = self.strategy.should_place_order(signal)
            if not should_place:
                logger.debug("不满足下单条件", reason=reason)
                return

            logger.info("准备执行套利交易", signal_type=signal.signal_type, reason=reason)

            # 执行交易
            await self._execute_arbitrage_trade(signal)

        except Exception as e:
            logger.error("风险管理和交易执行失败", error=str(e))

    async def _process_trading_logic(self) -> None:
        """处理交易逻辑（异步版本）"""
        try:
            if not self.is_running or not self.is_trading_enabled:
                logger.debug("交易逻辑跳过", is_running=self.is_running, is_trading_enabled=self.is_trading_enabled)
                return

            # 检查价格数据完整性
            binance_prices = self.strategy.binance_prices
            lighter_prices = self.strategy.lighter_prices

            logger.debug("当前价格状态",
                        binance_prices=binance_prices,
                        lighter_prices=lighter_prices)

            # 确保两个交易所都有价格数据
            if (binance_prices['bid'] <= 0 or binance_prices['ask'] <= 0 or
                lighter_prices['bid'] <= 0 or lighter_prices['ask'] <= 0):
                logger.debug("价格数据不完整，跳过交易逻辑")
                return

            # 获取当前Lighter价格
            try:
                lighter_orderbook = self.lighter_client.get_orderbook(self.config['trading']['symbol'])

                # 更新策略价格
                if lighter_orderbook['bids'] and lighter_orderbook['asks']:
                    bid_price = float(lighter_orderbook['bids'][0][0])
                    ask_price = float(lighter_orderbook['asks'][0][0])

                    # 尝试获取真实成交价
                    try:
                        lighter_price = self.lighter_client.get_current_price(self.config['trading']['symbol'])
                        # 更新策略价格（包含最新价）
                        self.strategy.update_lighter_prices(bid_price, ask_price, lighter_price)
                    except Exception:
                        # 如果获取不到真实成交价，只传递买卖价，让策略计算中间价
                        self.strategy.update_lighter_prices(bid_price, ask_price)

            except Exception as e:
                logger.debug("获取Lighter价格失败", error=str(e))
                return

            # 分析价差并生成信号
            signal = self.strategy.analyze_spread()
            if not signal:
                logger.debug("未生成交易信号")
                return

            logger.info("生成交易信号", signal_type=signal.signal_type, diff_rate=signal.diff_rate, confidence=signal.confidence)

            # 记录价差数据
            if self.trade_recorder:
                await self._record_spread_data(signal)

            # 检查风险管理
            if not await self._check_risk_management():
                logger.debug("风险管理检查未通过")
                return

            # 检查是否应该下单
            should_place, reason = self.strategy.should_place_order(signal)
            if not should_place:
                logger.debug("不满足下单条件", reason=reason)
                return

            logger.info("准备执行套利交易", signal_type=signal.signal_type, reason=reason)

            # 执行交易
            await self._execute_arbitrage_trade(signal)

        except Exception as e:
            logger.error("处理交易逻辑失败", error=str(e))
            self.stats['error_count'] += 1

    async def _check_risk_management(self) -> bool:
        """检查风险管理"""
        try:
            # 获取账户余额
            binance_balance = await self.binance_client.get_balance()
            lighter_balance = await self.lighter_client.get_balance()

            # 风险检查
            risk_check = self.risk_manager.check_risk(
                binance_balance=binance_balance,
                lighter_balance=lighter_balance,
                current_positions=self.strategy.active_orders
            )

            if not risk_check['allowed']:
                logger.warning("风险检查失败", reason=risk_check['reason'])
                return False

            return True

        except Exception as e:
            logger.error("风险检查失败", error=str(e))
            return False

    async def _execute_arbitrage_trade(self, signal: ArbitrageSignal) -> None:
        """执行套利交易"""
        try:
            logger.info("开始执行套利交易", signal=signal)

            # 计算交易数量 - 修复orderbooks访问问题
            try:
                # 获取Binance订单簿
                binance_orderbook = self.binance_client.orderbooks.get(self.strategy.symbol)

                # 获取Lighter订单簿 - 使用正确的方法
                lighter_orderbook_data = self.lighter_client.get_orderbook(self.strategy.symbol)

                if not binance_orderbook or not lighter_orderbook_data:
                    logger.warning("订单簿数据不完整")
                    return

                binance_bid_amount = binance_orderbook.bids[0][1] if binance_orderbook.bids else 0
                lighter_bid_amount = lighter_orderbook_data['bids'][0][1] if lighter_orderbook_data['bids'] else 0

            except Exception as e:
                logger.warning("获取订单簿数据失败，使用默认交易量", error=str(e))
                binance_bid_amount = 0.1  # 默认值
                lighter_bid_amount = 0.1  # 默认值

            # 获取最大持仓量
            max_position = self.config['trading']['max_position_size']

            amount = self.strategy.calculate_order_amount(
                signal, binance_bid_amount, lighter_bid_amount, max_position
            )

            if amount <= 0:
                logger.warning("计算的交易数量无效", amount=amount)
                return

            # 创建交易记录
            trade_type = TradeType.BUY_ARBITRAGE if signal.signal_type == SignalType.BUY else TradeType.SELL_ARBITRAGE
            expected_profit = Decimal(str(signal.expected_profit)) if signal.expected_profit else None
            spread_at_entry = Decimal(str(signal.spread_pct)) if signal.spread_pct else None

            trade_record = await self.trade_recorder.create_trade_record(
                symbol=self.config['trading']['symbol'],
                trade_type=trade_type,
                expected_profit=expected_profit,
                spread_at_entry=spread_at_entry,
                position_size=Decimal(str(amount))
            )

            # 执行交易
            if signal.signal_type == SignalType.BUY:
                await self._execute_buy_arbitrage(amount, signal, trade_record.id)
            elif signal.signal_type == SignalType.SELL:
                await self._execute_sell_arbitrage(amount, signal, trade_record.id)

            # 注意：total_trades在对冲完成后统计，避免重复计算

        except Exception as e:
            logger.error("执行套利交易失败", error=str(e))
            self.stats['failed_trades'] += 1
            self.stats['total_trades'] += 1  # 失败的交易也要计入总数

    async def _execute_buy_arbitrage(self, amount: float, signal: ArbitrageSignal, trade_id: str) -> None:
        """执行买入套利（Binance买入，Lighter卖出）"""
        try:
            start_time = time.time()

            # 在Binance以买一价下买单
            binance_bid_price = self.strategy.binance_prices['bid']

            if self.is_paper_trading:
                # 模拟交易
                binance_order = type('Order', (), {
                    'id': f'paper_binance_{int(time.time() * 1000)}',
                    'status': 'filled',
                    'price': binance_bid_price,
                    'amount': amount,
                    'side': 'buy'  # 添加side属性
                })()
                logger.info("模拟交易：Binance买入订单", order_id=binance_order.id, price=binance_bid_price, amount=amount)
            else:
                # 真实交易
                binance_order = await self.binance_client.place_order(
                    symbol=self.strategy.symbol,
                    side='buy',
                    amount=amount,
                    price=binance_bid_price,
                    order_type='limit'
                )

            # 更新交易记录
            await self.trade_recorder.update_binance_order(
                trade_id=trade_id,
                order_id=binance_order.id,
                side=OrderSide.BUY,
                price=Decimal(str(binance_bid_price)),
                quantity=Decimal(str(amount)),
                status=TradeStatus.EXECUTED if self.is_paper_trading else TradeStatus.PENDING
            )

            # 记录活跃订单
            order_info = OrderInfo(
                exchange='binance',
                order_id=binance_order.id,
                symbol=self.strategy.symbol,
                side='buy',
                amount=amount,
                price=binance_bid_price,
                status=binance_order.status,
                timestamp=time.time(),
                trade_id=trade_id  # 添加交易ID关联
            )
            self.strategy.add_active_order('binance', order_info)

            trading_logger.order_placed(
                'binance', 'buy', amount, binance_bid_price,
                self.strategy.symbol, binance_order.id
            )

            if self.is_paper_trading:
                # 模拟订单立即成交，执行对冲
                await self._execute_hedge_trade(binance_order, trade_id, start_time)
            else:
                # 启动订单监控
                asyncio.create_task(self._monitor_order('binance', binance_order.id, trade_id))

        except Exception as e:
            logger.error("执行买入套利失败", error=str(e), trade_id=trade_id)
            await self.trade_recorder.fail_trade(trade_id, str(e))
            raise

    async def _execute_sell_arbitrage(self, amount: float, signal: ArbitrageSignal, trade_id: str) -> None:
        """执行卖出套利（Binance卖出，Lighter买入）"""
        try:
            start_time = time.time()

            # 在Binance以卖一价下卖单
            binance_ask_price = self.strategy.binance_prices['ask']

            if self.is_paper_trading:
                # 模拟交易
                binance_order = type('Order', (), {
                    'id': f'paper_binance_{int(time.time() * 1000)}',
                    'status': 'filled',
                    'price': binance_ask_price,
                    'amount': amount,
                    'side': 'sell'  # 添加side属性
                })()
                logger.info("模拟交易：Binance卖出订单", order_id=binance_order.id, price=binance_ask_price, amount=amount)
            else:
                # 真实交易
                binance_order = await self.binance_client.place_order(
                    symbol=self.strategy.symbol,
                    side='sell',
                    amount=amount,
                    price=binance_ask_price,
                    order_type='limit'
                )

            # 更新交易记录
            await self.trade_recorder.update_binance_order(
                trade_id=trade_id,
                order_id=binance_order.id,
                side=OrderSide.SELL,
                price=Decimal(str(binance_ask_price)),
                quantity=Decimal(str(amount)),
                status=TradeStatus.EXECUTED if self.is_paper_trading else TradeStatus.PENDING
            )

            # 记录活跃订单
            order_info = OrderInfo(
                exchange='binance',
                order_id=binance_order.id,
                symbol=self.strategy.symbol,
                side='sell',
                amount=amount,
                price=binance_ask_price,
                status=binance_order.status,
                timestamp=time.time(),
                trade_id=trade_id  # 添加交易ID关联
            )
            self.strategy.add_active_order('binance', order_info)

            trading_logger.order_placed(
                'binance', 'sell', amount, binance_ask_price,
                self.strategy.symbol, binance_order.id
            )

            if self.is_paper_trading:
                # 模拟订单立即成交，执行对冲
                await self._execute_hedge_trade(binance_order, trade_id, start_time)
            else:
                # 启动订单监控
                asyncio.create_task(self._monitor_order('binance', binance_order.id, trade_id))

        except Exception as e:
            logger.error("执行卖出套利失败", error=str(e), trade_id=trade_id)
            await self.trade_recorder.fail_trade(trade_id, str(e))
            raise

    async def _monitor_order(self, exchange: str, order_id: str, trade_id: str) -> None:
        """监控订单状态"""
        try:
            timeout = self.config['trading'].get('order_timeout', 30)
            start_time = time.time()

            while time.time() - start_time < timeout:
                # 检查订单状态
                if exchange == 'binance':
                    order = await self.binance_client.get_order_status(order_id, self.strategy.symbol)
                    client = self.binance_client
                else:
                    order = await self.lighter_client.get_order_status(order_id, self.strategy.symbol)
                    client = self.lighter_client

                if not order:
                    logger.warning("无法获取订单状态", exchange=exchange, order_id=order_id)
                    break

                # 检查订单是否成交
                if order.status in ['filled', 'closed']:
                    logger.info("订单成交", exchange=exchange, order_id=order_id)
                    await self._handle_order_filled(exchange, order, trade_id)
                    break

                # 检查是否应该取消订单
                should_cancel, reason = self.strategy.should_cancel_order(exchange, order.price)
                if should_cancel:
                    logger.info("取消订单", exchange=exchange, order_id=order_id, reason=reason)
                    await client.cancel_order(order_id, self.strategy.symbol)
                    self.strategy.remove_active_order(exchange)
                    break

                await asyncio.sleep(1)  # 每秒检查一次

            # 超时处理
            if time.time() - start_time >= timeout:
                logger.warning("订单监控超时", exchange=exchange, order_id=order_id)
                if exchange == 'binance':
                    await self.binance_client.cancel_order(order_id, self.strategy.symbol)
                else:
                    await self.lighter_client.cancel_order(order_id, self.strategy.symbol)
                self.strategy.remove_active_order(exchange)

        except Exception as e:
            logger.error("监控订单失败", exchange=exchange, order_id=order_id, error=str(e))

    async def _handle_order_filled(self, exchange: str, order, trade_id: str) -> None:
        """处理订单成交"""
        try:
            self.strategy.remove_active_order(exchange)

            # 立即执行对冲
            await self._execute_hedge(order, trade_id)

            # 更新统计
            self.stats['total_trades'] += 1
            self.strategy.update_last_trade_time()

            trading_logger.trade_executed(
                exchange, order.side, order.filled,
                order.price, order.symbol, order.id
            )

        except Exception as e:
            logger.error("处理订单成交失败", error=str(e))

    async def _execute_hedge_trade(self, filled_order, trade_id: str, start_time: float) -> None:
        """执行对冲交易（新方法）"""
        try:
            # 获取对冲方向和价格
            if filled_order.side == 'buy':
                # Binance买入后，在Lighter卖出对冲
                hedge_side = 'sell'
                hedge_price = self.strategy.lighter_prices['bid']  # 以Lighter买一价立即卖出
            else:
                # Binance卖出后，在Lighter买入对冲
                hedge_side = 'buy'
                hedge_price = self.strategy.lighter_prices['ask']  # 以Lighter卖一价立即买入

            if self.is_paper_trading:
                # 模拟对冲交易
                hedge_order = type('Order', (), {
                    'id': f'paper_lighter_{int(time.time() * 1000)}',
                    'status': 'filled',
                    'price': hedge_price,
                    'amount': filled_order.amount,
                    'side': hedge_side
                })()
                logger.info("模拟对冲：Lighter订单", order_id=hedge_order.id, side=hedge_side, price=hedge_price, amount=filled_order.amount)
            else:
                # 真实对冲交易
                hedge_order = await self.lighter_client.place_order(
                    symbol=self.strategy.symbol,
                    side=hedge_side,
                    amount=filled_order.amount,
                    order_type='market'  # 市价单确保立即成交
                )

            # 更新Lighter订单记录
            await self.trade_recorder.update_lighter_order(
                trade_id=trade_id,
                order_id=hedge_order.id,
                side=OrderSide.BUY if hedge_side == 'buy' else OrderSide.SELL,
                price=Decimal(str(hedge_price)),
                quantity=Decimal(str(hedge_order.amount)),
                status=TradeStatus.EXECUTED
            )

            # 计算实际利润
            actual_profit = self._calculate_trade_profit(filled_order, hedge_order)

            # 计算执行时间
            execution_time_ms = int((time.time() - start_time) * 1000)

            # 计算滑点（简化版）
            expected_hedge_price = self.strategy.lighter_prices.get('ask' if hedge_side == 'buy' else 'bid')
            slippage = abs(hedge_price - expected_hedge_price) / expected_hedge_price if expected_hedge_price else 0

            # 完成交易记录
            await self.trade_recorder.complete_trade(
                trade_id=trade_id,
                actual_profit=Decimal(str(actual_profit)),
                execution_time_ms=execution_time_ms,
                slippage=Decimal(str(slippage))
            )

            # 更新统计
            self.stats['successful_trades'] += 1
            self.stats['total_trades'] += 1  # 成功的交易计入总数
            self.stats['total_profit'] += actual_profit
            self.stats['last_trade_time'] = time.time()

            # 立即推送统计数据更新到Web界面
            if self.web_status_update_callback:
                try:
                    status = self.get_status()
                    self.web_status_update_callback(status)
                except Exception as e:
                    logger.debug("推送统计数据更新失败", error=str(e))

            # 推送交易记录更新到Web界面
            if self.web_trade_update_callback:
                try:
                    # 获取最新的交易记录
                    trade_data = {
                        'trade_id': trade_id,
                        'symbol': self.strategy.symbol,
                        'trade_type': 'arbitrage',
                        'actual_profit': actual_profit,
                        'execution_time_ms': execution_time_ms,
                        'timestamp': time.time(),
                        'status': 'completed'
                    }
                    self.web_trade_update_callback(trade_data)
                except Exception as e:
                    logger.debug("推送交易记录更新失败", error=str(e))

            # 记录交易日志
            if hasattr(trading_logger, 'arbitrage_completed'):
                trading_logger.arbitrage_completed(
                    filled_order.side, filled_order.amount, filled_order.price,
                    hedge_side, hedge_order.amount, hedge_price,
                    actual_profit, execution_time_ms
                )
            else:
                # 使用基础日志记录
                trading_logger.trade_executed(
                    "arbitrage", f"{filled_order.side}-{hedge_side}",
                    filled_order.amount, filled_order.price,
                    self.strategy.symbol, f"{filled_order.id}-{hedge_order.id}"
                )

        except Exception as e:
            logger.error("执行对冲交易失败", error=str(e), trade_id=trade_id)
            await self.trade_recorder.fail_trade(trade_id, f"对冲失败: {str(e)}")
            raise

    async def _execute_hedge(self, filled_order, trade_id: str) -> None:
        """执行对冲交易（保持兼容性）"""
        start_time = time.time()
        await self._execute_hedge_trade(filled_order, trade_id, start_time)

    def _calculate_trade_profit(self, order1, order2) -> float:
        """计算交易利润"""
        try:
            # 根据订单方向计算利润
            if hasattr(order1, 'side'):
                if order1.side == 'buy':
                    # Binance买入，Lighter卖出
                    binance_cost = order1.price * order1.amount
                    lighter_revenue = order2.price * order2.amount
                    profit = lighter_revenue - binance_cost
                else:
                    # Binance卖出，Lighter买入
                    binance_revenue = order1.price * order1.amount
                    lighter_cost = order2.price * order2.amount
                    profit = binance_revenue - lighter_cost
            else:
                # 简化计算
                profit = abs(order1.price - order2.price) * min(order1.amount, order2.amount)

            # 扣除手续费（简化，实际应从订单中获取）
            fee_rate = 0.001  # 0.1% 手续费
            total_amount = order1.amount + order2.amount
            fees = (order1.price * order1.amount + order2.price * order2.amount) * fee_rate

            return profit - fees

        except Exception as e:
            logger.error("计算交易利润失败", error=str(e))
            return 0.0

    async def start(self) -> None:
        """启动套利引擎"""
        try:
            if not self.is_initialized:
                await self.initialize()

            self.is_running = True
            self.is_trading_enabled = True  # 确保交易已启用
            self.stats['start_time'] = time.time()

            logger.info("套利引擎启动成功", is_running=self.is_running, is_trading_enabled=self.is_trading_enabled)

            # 立即更新一次统计数据显示
            await self._update_stats_display()

            # 启动扩展模块
            await self._start_extension_modules()

            # 启动定期检查任务
            periodic_task = asyncio.create_task(self._periodic_check())

            # 启动系统监控任务
            monitoring_task = asyncio.create_task(self._system_monitoring_task())

            # 启动数据清理任务
            cleanup_task = asyncio.create_task(self._data_cleanup_task())

            # 启动扩展模块任务
            extension_tasks = await self._start_extension_tasks()

            # 等待停止信号
            try:
                all_tasks = [periodic_task, monitoring_task, cleanup_task] + extension_tasks
                await asyncio.gather(*all_tasks)
            except asyncio.CancelledError:
                logger.info("套利引擎任务被取消")

        except Exception as e:
            logger.error("启动套利引擎失败", error=str(e))
            raise

    async def _start_extension_modules(self):
        """启动扩展模块"""
        try:
            # 启动系统监控
            if self.system_monitor:
                await self.system_monitor.start()
                logger.info("✅ 系统监控已启动")

            # 启动订单管理器
            if self.order_manager:
                await self.order_manager.start()
                logger.info("✅ 订单管理器已启动")

            # 启动性能优化器
            if self.performance_optimizer:
                await self.performance_optimizer.start()
                logger.info("✅ 性能优化器已启动")

        except Exception as e:
            logger.error("启动扩展模块失败", error=str(e))

    async def _start_extension_tasks(self) -> list:
        """启动扩展模块任务"""
        tasks = []

        try:
            # 系统监控任务已在start()中启动，这里不需要额外任务
            # 订单管理器任务已在start()中启动，这里不需要额外任务
            # 性能优化器任务已在start()中启动，这里不需要额外任务

            logger.info("扩展模块任务启动完成")

        except Exception as e:
            logger.error("启动扩展模块任务失败", error=str(e))

        return tasks

    async def _periodic_check(self) -> None:
        """定期检查任务"""
        while self.is_running:
            try:
                # 风险检查
                await self._check_risk_management()

                # 记录风险指标
                await self._record_risk_metrics()

                # 检查活跃订单状态
                await self._check_active_orders()

                # 休眠
                await asyncio.sleep(30)  # 每30秒检查一次

            except Exception as e:
                logger.error("定期检查失败", error=str(e))
                await asyncio.sleep(60)  # 出错时等待更长时间

    async def _system_monitoring_task(self) -> None:
        """系统监控任务"""
        while self.is_running:
            try:
                # 记录系统状态
                await self._record_system_status()

                # 检查连接状态
                await self._check_connections()

                # 休眠
                await asyncio.sleep(60)  # 每分钟记录一次

            except Exception as e:
                logger.error("系统监控失败", error=str(e))
                await asyncio.sleep(120)

    async def _data_cleanup_task(self) -> None:
        """数据清理任务"""
        while self.is_running:
            try:
                # 每天清理一次旧数据
                await asyncio.sleep(24 * 60 * 60)  # 24小时

                if self.is_running:
                    await self.db_manager.cleanup_old_data(days=30)

                    # 备份数据库
                    if datetime.now(timezone.utc).hour == 2:  # 凌晨2点备份
                        await self.db_manager.backup_database()

            except Exception as e:
                logger.error("数据清理失败", error=str(e))
                await asyncio.sleep(60 * 60)  # 出错时等待1小时

    async def _check_active_orders(self) -> None:
        """检查活跃订单状态"""
        try:
            active_orders = self.strategy.get_active_orders()

            for exchange, order_info in active_orders.items():
                if order_info and hasattr(order_info, 'trade_id'):
                    # 检查订单是否超时
                    if time.time() - order_info.timestamp > 300:  # 5分钟超时
                        logger.warning("订单超时，准备取消",
                                     exchange=exchange,
                                     order_id=order_info.order_id)

                        # 取消超时订单
                        if exchange == 'binance' and self.binance_client:
                            try:
                                await self.binance_client.cancel_order(
                                    order_info.symbol, order_info.order_id
                                )
                                await self.trade_recorder.cancel_trade(
                                    order_info.trade_id, "订单超时"
                                )
                            except Exception as e:
                                logger.error("取消Binance订单失败", error=str(e))

                        # 从活跃订单中移除
                        self.strategy.remove_active_order(exchange)

        except Exception as e:
            logger.error("检查活跃订单失败", error=str(e))

    async def _check_connections(self) -> None:
        """检查连接状态"""
        try:
            # 检查Binance连接
            if self.binance_client:
                try:
                    # 测试网络延迟（简化版）
                    network_latency = 0.0
                    if not self.is_paper_trading:
                        start_time = time.time()
                        try:
                            # 简单的ticker请求来测试延迟
                            await self.binance_client.get_ticker(self.config['trading']['symbol'])
                            network_latency = (time.time() - start_time) * 1000
                        except:
                            network_latency = 999.9
                    else:
                        # 模拟交易模式下使用模拟延迟
                        network_latency = 50.0
                    self.connection_status['binance'] = True
                except Exception:
                    self.connection_status['binance'] = False
                    logger.warning("Binance连接断开")

            # 检查Lighter连接
            if self.lighter_client:
                try:
                    # 简单的连接检查
                    self.connection_status['lighter'] = True
                except Exception:
                    self.connection_status['lighter'] = False
                    logger.warning("Lighter连接断开")

            # 检查WebSocket连接状态
            # 这里可以添加WebSocket连接状态检查

        except Exception as e:
            logger.error("检查连接状态失败", error=str(e))

    async def stop(self) -> None:
        """停止套利引擎"""
        try:
            logger.info("正在停止套利引擎...")

            self.is_running = False
            self.is_trading_enabled = False

            # 取消所有活跃订单
            await self._cancel_all_active_orders()

            # 记录最终系统状态
            await self._record_system_status()

            # 计算并保存当日性能指标
            await self._save_daily_performance()

            # 停止客户端
            if self.binance_client:
                await self.binance_client.close()

            if self.lighter_client:
                await self.lighter_client.close()

            # 停止扩展模块
            await self._stop_extension_modules()

            # 关闭数据库连接
            if self.db_manager:
                await self.db_manager.close()

            logger.info("套利引擎已停止")

        except Exception as e:
            logger.error("停止套利引擎失败", error=str(e))

    async def _stop_extension_modules(self):
        """停止扩展模块"""
        try:
            # 停止性能优化器
            if self.performance_optimizer:
                await self.performance_optimizer.stop()
                logger.info("✅ 性能优化器已停止")

            # 停止订单管理器
            if self.order_manager:
                await self.order_manager.stop()
                logger.info("✅ 订单管理器已停止")

            # 停止系统监控
            if self.system_monitor:
                await self.system_monitor.stop()
                logger.info("✅ 系统监控已停止")

        except Exception as e:
            logger.error("停止扩展模块失败", error=str(e))

    async def _cancel_all_active_orders(self) -> None:
        """取消所有活跃订单"""
        try:
            # 检查策略是否有get_active_orders方法
            if not hasattr(self.strategy, 'get_active_orders'):
                logger.info("策略类没有活跃订单跟踪，跳过取消订单")
                return

            active_orders = self.strategy.get_active_orders()

            for exchange, order_info in active_orders.items():
                if order_info:
                    try:
                        if exchange == 'binance' and self.binance_client:
                            await self.binance_client.cancel_order(
                                order_info.symbol, order_info.order_id
                            )

                        # 取消交易记录
                        if hasattr(order_info, 'trade_id'):
                            await self.trade_recorder.cancel_trade(
                                order_info.trade_id, "系统停止"
                            )

                        logger.info("取消活跃订单",
                                  exchange=exchange,
                                  order_id=order_info.order_id)

                    except Exception as e:
                        logger.error("取消订单失败",
                                   exchange=exchange,
                                   order_id=order_info.order_id,
                                   error=str(e))

            # 清空活跃订单
            if hasattr(self.strategy, 'clear_active_orders'):
                self.strategy.clear_active_orders()

        except Exception as e:
            logger.error("取消所有活跃订单失败", error=str(e))

    async def _save_daily_performance(self) -> None:
        """保存当日性能指标"""
        try:
            today = datetime.now(timezone.utc)
            performance = await self.trade_recorder.calculate_daily_performance(today)
            await self.db_manager.save_performance_metrics(performance)

            logger.info("保存当日性能指标",
                       total_trades=performance.total_trades,
                       net_profit=float(performance.net_profit),
                       success_rate=performance.success_rate)

        except Exception as e:
            logger.error("保存当日性能指标失败", error=str(e))

    def get_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        # 计算成功率
        win_rate = 0.0
        if self.stats['total_trades'] > 0:
            win_rate = (self.stats['successful_trades'] / self.stats['total_trades']) * 100

        # 计算运行时间
        uptime = 0
        if self.stats.get('start_time'):
            uptime = time.time() - self.stats['start_time']

        # 更新统计数据
        updated_stats = self.stats.copy()
        updated_stats.update({
            'win_rate': win_rate,
            'uptime': uptime
        })

        return {
            'is_running': self.is_running,
            'is_initialized': self.is_initialized,
            'is_trading_enabled': self.is_trading_enabled,
            'is_paper_trading': self.is_paper_trading,
            'connection_status': self.connection_status,
            'stats': updated_stats,
            'strategy_status': self.strategy.get_strategy_status() if self.strategy else {},
            'risk_status': self.risk_manager.get_risk_status() if self.risk_manager else {},
            'assets': None  # 总资产信息将通过单独的API获取
        }

    async def _record_spread_data(self, signal: ArbitrageSignal) -> None:
        """记录价差数据"""
        try:
            binance_prices = self.strategy.binance_prices
            lighter_prices = self.strategy.lighter_prices

            if all(key in binance_prices for key in ['bid', 'ask']) and \
               all(key in lighter_prices for key in ['bid', 'ask']):

                await self.trade_recorder.record_spread_data(
                    symbol=self.config['trading']['symbol'],
                    binance_bid=Decimal(str(binance_prices['bid'])),
                    binance_ask=Decimal(str(binance_prices['ask'])),
                    lighter_bid=Decimal(str(lighter_prices['bid'])),
                    lighter_ask=Decimal(str(lighter_prices['ask'])),
                    ma_spread=Decimal(str(signal.ma_value)) if signal.ma_value else None,
                    signal=signal.signal_type.value if signal.signal_type else None,
                    signal_strength=signal.confidence
                )
        except Exception as e:
            logger.error("记录价差数据失败", error=str(e))

    async def get_total_assets(self) -> Dict[str, float]:
        """
        获取总资产信息

        Returns:
            包含各交易所资产信息的字典
        """
        try:
            total_assets = {
                'binance_usdt': 0.0,
                'binance_btc': 0.0,
                'lighter_usdt': 0.0,
                'lighter_btc': 0.0,
                'total_usdt_value': 0.0,
                'btc_price': 0.0
            }

            # 获取当前BTC价格用于计算总价值
            btc_price = 0.0
            try:
                if self.strategy and hasattr(self.strategy, 'binance_prices'):
                    btc_price = self.strategy.binance_prices.get('last', 0.0)
                if btc_price == 0.0 and self.strategy and hasattr(self.strategy, 'lighter_prices'):
                    btc_price = self.strategy.lighter_prices.get('last', 0.0)
                total_assets['btc_price'] = btc_price
            except Exception as e:
                logger.debug("获取BTC价格失败", error=str(e))

            # 获取Binance余额
            try:
                if self.binance_client:
                    binance_balance = await self.binance_client.get_balance()
                    total_assets['binance_usdt'] = binance_balance.get('total', {}).get('USDT', 0.0)
                    total_assets['binance_btc'] = binance_balance.get('total', {}).get('BTC', 0.0)
            except Exception as e:
                logger.debug("获取Binance余额失败", error=str(e))

            # 获取Lighter余额
            try:
                if self.lighter_client:
                    lighter_balance = await self.lighter_client.get_balance()
                    total_assets['lighter_usdt'] = lighter_balance.get('total', {}).get('USDT', 0.0)
                    total_assets['lighter_btc'] = lighter_balance.get('total', {}).get('BTC', 0.0)
            except Exception as e:
                logger.debug("获取Lighter余额失败", error=str(e))

            # 计算总USDT价值
            if btc_price > 0:
                total_btc = total_assets['binance_btc'] + total_assets['lighter_btc']
                total_usdt = total_assets['binance_usdt'] + total_assets['lighter_usdt']
                total_assets['total_usdt_value'] = total_usdt + (total_btc * btc_price)
            else:
                total_assets['total_usdt_value'] = total_assets['binance_usdt'] + total_assets['lighter_usdt']

            logger.debug("总资产计算完成",
                        binance_usdt=total_assets['binance_usdt'],
                        binance_btc=total_assets['binance_btc'],
                        lighter_usdt=total_assets['lighter_usdt'],
                        lighter_btc=total_assets['lighter_btc'],
                        total_usdt_value=total_assets['total_usdt_value'])

            return total_assets

        except Exception as e:
            logger.error("获取总资产失败", error=str(e))
            return {
                'binance_usdt': 0.0,
                'binance_btc': 0.0,
                'lighter_usdt': 0.0,
                'lighter_btc': 0.0,
                'total_usdt_value': 0.0,
                'btc_price': 0.0
            }

    async def _record_system_status(self) -> None:
        """记录系统状态"""
        try:
            # 获取系统性能指标
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent

            # 测试网络延迟（简化版）
            network_latency = 0.0
            if self.binance_client and not self.is_paper_trading:
                start_time = time.time()
                try:
                    # 简单的ticker请求来测试延迟
                    await self.binance_client.get_ticker(self.config['trading']['symbol'])
                    network_latency = (time.time() - start_time) * 1000
                except:
                    network_latency = 999.9
            else:
                # 模拟交易模式下使用模拟延迟
                network_latency = 50.0

            status = SystemStatus(
                timestamp=datetime.now(timezone.utc),
                is_running=self.is_running,
                is_trading_enabled=self.is_trading_enabled,
                is_paper_trading=self.is_paper_trading,
                binance_connected=self.connection_status['binance'],
                lighter_connected=self.connection_status['lighter'],
                websocket_connected=self.connection_status['websocket'],
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                network_latency_ms=network_latency,
                last_price_update=self.last_price_update,
                last_trade_execution=datetime.fromtimestamp(self.stats['last_trade_time']) if self.stats['last_trade_time'] else None,
                error_count=self.stats['error_count']
            )

            await self.db_manager.save_system_status(status)

        except Exception as e:
            logger.error("记录系统状态失败", error=str(e))

    async def _record_risk_metrics(self) -> None:
        """记录风险指标"""
        try:
            risk_data = self.risk_manager.get_risk_metrics()
            active_trades_count = await self.trade_recorder.get_active_trade_count()

            metrics = RiskMetrics(
                timestamp=datetime.now(timezone.utc),
                total_position_size=Decimal(str(risk_data.get('total_position_size', 0))),
                position_limit=Decimal(str(risk_data.get('position_limit', 0))),
                position_utilization=risk_data.get('position_utilization', 0.0),
                total_pnl=Decimal(str(self.stats['total_profit'])),
                daily_pnl=Decimal(str(risk_data.get('daily_pnl', 0))),
                max_drawdown=Decimal(str(risk_data.get('max_drawdown', 0))),
                unrealized_pnl=Decimal(str(risk_data.get('unrealized_pnl', 0))),
                active_trades_count=active_trades_count,
                pending_orders_count=risk_data.get('pending_orders_count', 0),
                failed_trades_count=self.stats['failed_trades'],
                success_rate=self.stats['successful_trades'] / max(self.stats['total_trades'], 1),
                spread_volatility=risk_data.get('spread_volatility', 0.0),
                price_volatility=risk_data.get('price_volatility', 0.0),
                liquidity_risk=risk_data.get('liquidity_risk', 0.0),
                risk_score=risk_data.get('risk_score', 0.0),
                risk_level=risk_data.get('risk_level', 'LOW'),
                trading_halted=not self.is_trading_enabled
            )

            await self.db_manager.save_risk_metrics(metrics)

        except Exception as e:
            logger.error("记录风险指标失败", error=str(e))

    def set_web_callbacks(self, price_callback: callable = None, status_callback: callable = None, log_callback: callable = None, trade_callback: callable = None):
        """
        设置Web界面回调函数

        Args:
            price_callback: 价格更新回调函数
            status_callback: 状态更新回调函数
            log_callback: 日志更新回调函数
            trade_callback: 交易更新回调函数
        """
        self.web_price_update_callback = price_callback
        self.web_status_update_callback = status_callback
        self.web_log_callback = log_callback
        self.web_trade_update_callback = trade_callback
        logger.info("Web回调函数已设置")

    def send_log_to_web(self, level: str, message: str, **kwargs):
        """发送日志到Web界面"""
        if self.web_log_callback:
            try:
                self.web_log_callback(level, message, **kwargs)
            except Exception as e:
                # 避免日志回调失败影响主要功能
                pass