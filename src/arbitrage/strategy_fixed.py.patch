
def _generate_signal(self, diff_rate: float, ma_value: float,
                    binance_price: float, lighter_price: float) -> Optional[ArbitrageSignal]:
    """
    修复后的信号生成逻辑
    """
    # 检查价差是否超过最大阈值
    if abs(diff_rate) > self.max_spread_threshold:
        logger.warning("价差超过最大阈值", diff_rate=diff_rate, threshold=self.max_spread_threshold)
        return None

    # 计算实际价差 (绝对值)
    price_diff = abs(binance_price - lighter_price)
    
    # 计算最小盈利要求 (包含交易费用)
    min_profit_with_fees = self.min_profit_threshold + 0.0002  # 假设总费用0.02%
    
    # 检查是否满足最小盈利阈值
    if price_diff / min(binance_price, lighter_price) < min_profit_with_fees:
        return None

    # 修复后的信号逻辑
    if binance_price < lighter_price:
        # Binance便宜，在Binance买入，在Lighter卖出
        signal_type = SignalType.BUY
        expected_profit = (lighter_price - binance_price) * self.min_trade_amount
        reason = f"Binance价格低于Lighter {price_diff:.2f} USDT"
    elif binance_price > lighter_price:
        # Lighter便宜，在Lighter买入，在Binance卖出  
        signal_type = SignalType.SELL
        expected_profit = (binance_price - lighter_price) * self.min_trade_amount
        reason = f"Lighter价格低于Binance {price_diff:.2f} USDT"
    else:
        signal_type = SignalType.HOLD
        expected_profit = 0.0
        reason = "价格相等"

    # 只有预期利润为正时才生成信号
    if expected_profit <= 0:
        return None

    # 计算信心度 (基于价差大小)
    confidence = min(price_diff / (binance_price * min_profit_with_fees), 1.0)
    
    # 最低信心度要求
    if confidence < 0.5:
        return None

    # 计算价差百分比
    spread_pct = price_diff / min(binance_price, lighter_price) * 100

    return ArbitrageSignal(
        signal_type=signal_type,
        binance_price=binance_price,
        lighter_price=lighter_price,
        diff_rate=diff_rate,
        ma_value=ma_value,
        confidence=confidence,
        timestamp=time.time(),
        reason=reason,
        expected_profit=expected_profit,
        spread_pct=spread_pct
    )
